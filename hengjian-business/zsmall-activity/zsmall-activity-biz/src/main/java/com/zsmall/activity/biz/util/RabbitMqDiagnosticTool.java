package com.zsmall.activity.biz.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.zsmall.activity.entity.domain.dto.productActivity.ActivityExpireMq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;
import java.util.Properties;

/**
 * RabbitMQ TTL队列诊断工具
 * 用于检查和测试活动过期TTL队列的配置和功能
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RabbitMqDiagnosticTool {

    private final RabbitAdmin rabbitAdmin;
    private final RabbitTemplate rabbitTemplate;

    /**
     * 诊断TTL队列配置
     */
    public void diagnoseTtlQueueConfiguration() {
        log.info("=== 开始诊断TTL队列配置 ===");
        
        try {
            // 检查队列是否存在
            checkQueueExists(RabbitMqConstant.ACTIVITY_EXPIRE_TTL_QUEUE);
            checkQueueExists(RabbitMqConstant.ACTIVITY_EXPIRE_PROCESS_QUEUE);
            
            // 检查交换机是否存在
            checkExchangeExists(RabbitMqConstant.ACTIVITY_EXPIRE_EXCHANGE);
            checkExchangeExists(RabbitMqConstant.ACTIVITY_EXPIRE_DLX_EXCHANGE);
            
            // 获取队列属性
            Properties ttlQueueProperties = rabbitAdmin.getQueueProperties(RabbitMqConstant.ACTIVITY_EXPIRE_TTL_QUEUE);
            if (ttlQueueProperties != null) {
                log.info("TTL队列属性: {}", ttlQueueProperties);
            } else {
                log.error("无法获取TTL队列属性，队列可能不存在");
            }
            
            Properties processQueueProperties = rabbitAdmin.getQueueProperties(RabbitMqConstant.ACTIVITY_EXPIRE_PROCESS_QUEUE);
            if (processQueueProperties != null) {
                log.info("处理队列属性: {}", processQueueProperties);
            } else {
                log.error("无法获取处理队列属性，队列可能不存在");
            }
            
        } catch (Exception e) {
            log.error("诊断TTL队列配置时发生错误", e);
        }
        
        log.info("=== TTL队列配置诊断完成 ===");
    }

    /**
     * 检查队列是否存在
     */
    private void checkQueueExists(String queueName) {
        try {
            Properties properties = rabbitAdmin.getQueueProperties(queueName);
            if (properties != null) {
                log.info("队列 [{}] 存在，消息数量: {}", queueName, properties.get("QUEUE_MESSAGE_COUNT"));
            } else {
                log.warn("队列 [{}] 不存在或无法访问", queueName);
            }
        } catch (Exception e) {
            log.error("检查队列 [{}] 时发生错误: {}", queueName, e.getMessage());
        }
    }

    /**
     * 检查交换机是否存在
     */
    private void checkExchangeExists(String exchangeName) {
        try {
            // 尝试声明交换机来检查是否存在
            rabbitAdmin.declareExchange(new org.springframework.amqp.core.DirectExchange(exchangeName, true, false));
            log.info("交换机 [{}] 存在或已成功声明", exchangeName);
        } catch (Exception e) {
            log.error("检查交换机 [{}] 时发生错误: {}", exchangeName, e.getMessage());
        }
    }

    /**
     * 发送测试TTL消息
     */
    public void sendTestTtlMessage(int ttlSeconds) {
        log.info("=== 发送测试TTL消息，TTL={}秒 ===", ttlSeconds);
        
        try {
            ActivityExpireMq testMq = new ActivityExpireMq();
            testMq.setActiveCode("TEST_" + System.currentTimeMillis());
            testMq.setType(1);
            
            String messageBody = JSONUtil.toJsonStr(testMq);
            long ttlMs = ttlSeconds * 1000L;
            
            rabbitTemplate.convertAndSend(
                RabbitMqConstant.ACTIVITY_EXPIRE_EXCHANGE,
                RabbitMqConstant.ACTIVITY_EXPIRE_ROUTING_KEY,
                messageBody,
                message -> {
                    message.getMessageProperties().setExpiration(String.valueOf(ttlMs));
                    return message;
                }
            );
            
            log.info("测试TTL消息发送成功: 活动编码={}, TTL={}ms, 预期过期时间={}", 
                testMq.getActiveCode(), ttlMs, DateUtil.formatDateTime(DateUtil.offsetSecond(new Date(), ttlSeconds)));
                
        } catch (Exception e) {
            log.error("发送测试TTL消息失败", e);
        }
    }

    /**
     * 重新声明所有队列和交换机
     */
    public void redeclareAllQueuesAndExchanges() {
        log.info("=== 重新声明所有队列和交换机 ===");
        
        try {
            // 声明交换机
            rabbitAdmin.declareExchange(new org.springframework.amqp.core.DirectExchange(
                RabbitMqConstant.ACTIVITY_EXPIRE_EXCHANGE, true, false));
            rabbitAdmin.declareExchange(new org.springframework.amqp.core.DirectExchange(
                RabbitMqConstant.ACTIVITY_EXPIRE_DLX_EXCHANGE, true, false));
            
            // 声明TTL队列
            Map<String, Object> ttlArguments = Map.of(
                "x-dead-letter-exchange", RabbitMqConstant.ACTIVITY_EXPIRE_DLX_EXCHANGE,
                "x-dead-letter-routing-key", RabbitMqConstant.ACTIVITY_EXPIRE_ROUTING_KEY
            );
            Queue ttlQueue = new Queue(RabbitMqConstant.ACTIVITY_EXPIRE_TTL_QUEUE, true, false, false, ttlArguments);
            rabbitAdmin.declareQueue(ttlQueue);
            
            // 声明处理队列
            Queue processQueue = new Queue(RabbitMqConstant.ACTIVITY_EXPIRE_PROCESS_QUEUE, true, false, false, null);
            rabbitAdmin.declareQueue(processQueue);
            
            // 声明绑定
            rabbitAdmin.declareBinding(new org.springframework.amqp.core.Binding(
                RabbitMqConstant.ACTIVITY_EXPIRE_TTL_QUEUE,
                org.springframework.amqp.core.Binding.DestinationType.QUEUE,
                RabbitMqConstant.ACTIVITY_EXPIRE_EXCHANGE,
                RabbitMqConstant.ACTIVITY_EXPIRE_ROUTING_KEY,
                null
            ));
            
            rabbitAdmin.declareBinding(new org.springframework.amqp.core.Binding(
                RabbitMqConstant.ACTIVITY_EXPIRE_PROCESS_QUEUE,
                org.springframework.amqp.core.Binding.DestinationType.QUEUE,
                RabbitMqConstant.ACTIVITY_EXPIRE_DLX_EXCHANGE,
                RabbitMqConstant.ACTIVITY_EXPIRE_ROUTING_KEY,
                null
            ));
            
            log.info("所有队列和交换机重新声明完成");
            
        } catch (Exception e) {
            log.error("重新声明队列和交换机时发生错误", e);
        }
    }

    /**
     * 清空所有相关队列
     */
    public void purgeAllQueues() {
        log.info("=== 清空所有相关队列 ===");
        
        try {
            rabbitAdmin.purgeQueue(RabbitMqConstant.ACTIVITY_EXPIRE_TTL_QUEUE);
            log.info("已清空TTL队列: {}", RabbitMqConstant.ACTIVITY_EXPIRE_TTL_QUEUE);
            
            rabbitAdmin.purgeQueue(RabbitMqConstant.ACTIVITY_EXPIRE_PROCESS_QUEUE);
            log.info("已清空处理队列: {}", RabbitMqConstant.ACTIVITY_EXPIRE_PROCESS_QUEUE);
            
        } catch (Exception e) {
            log.error("清空队列时发生错误", e);
        }
    }
}
