package com.zsmall.activity.biz.controller;

import com.hengjian.common.core.domain.R;
import com.zsmall.activity.biz.util.RabbitMqDiagnosticTool;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * RabbitMQ诊断控制器
 * 用于诊断和测试TTL队列功能
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Tag(name = "RabbitMQ诊断", description = "RabbitMQ TTL队列诊断和测试接口")
@RestController
@RequestMapping("/api/rabbitmq/diagnostic")
@RequiredArgsConstructor
@Slf4j
public class RabbitMqDiagnosticController {

    private final RabbitMqDiagnosticTool diagnosticTool;

    @Operation(summary = "诊断TTL队列配置", description = "检查TTL队列、交换机和绑定关系是否正确配置")
    @GetMapping("/diagnose")
    public R<String> diagnoseTtlQueue() {
        try {
            diagnosticTool.diagnoseTtlQueueConfiguration();
            return R.ok("TTL队列诊断完成，请查看日志获取详细信息");
        } catch (Exception e) {
            log.error("诊断TTL队列时发生错误", e);
            return R.fail("诊断失败: " + e.getMessage());
        }
    }

    @Operation(summary = "发送测试TTL消息", description = "发送一个测试消息到TTL队列，用于验证TTL功能")
    @PostMapping("/test-ttl")
    public R<String> sendTestTtlMessage(
            @Parameter(description = "TTL时间（秒）", example = "10")
            @RequestParam(defaultValue = "10") int ttlSeconds) {
        try {
            if (ttlSeconds <= 0 || ttlSeconds > 3600) {
                return R.fail("TTL时间必须在1-3600秒之间");
            }
            
            diagnosticTool.sendTestTtlMessage(ttlSeconds);
            return R.ok("测试TTL消息发送成功，将在" + ttlSeconds + "秒后过期并转发到处理队列");
        } catch (Exception e) {
            log.error("发送测试TTL消息时发生错误", e);
            return R.fail("发送失败: " + e.getMessage());
        }
    }

    @Operation(summary = "重新声明队列和交换机", description = "重新声明所有相关的队列、交换机和绑定关系")
    @PostMapping("/redeclare")
    public R<String> redeclareQueuesAndExchanges() {
        try {
            diagnosticTool.redeclareAllQueuesAndExchanges();
            return R.ok("队列和交换机重新声明完成");
        } catch (Exception e) {
            log.error("重新声明队列和交换机时发生错误", e);
            return R.fail("重新声明失败: " + e.getMessage());
        }
    }

    @Operation(summary = "清空所有队列", description = "清空TTL队列和处理队列中的所有消息")
    @PostMapping("/purge")
    public R<String> purgeAllQueues() {
        try {
            diagnosticTool.purgeAllQueues();
            return R.ok("所有队列已清空");
        } catch (Exception e) {
            log.error("清空队列时发生错误", e);
            return R.fail("清空失败: " + e.getMessage());
        }
    }
}
